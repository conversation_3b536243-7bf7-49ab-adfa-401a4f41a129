{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\reserve\\ReserveDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\reserve\\ReserveDetail.vue", "mtime": 1749224368825}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\reserve\\ReserveDetail.vue"], "names": [], "mappings": ";;QAoDQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,EAAE,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAE1B,CAAC;YACL,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;;;YAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAExB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;4BACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC;wBACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;4BACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1D,CAAC,CAAC;oBACN,CAAC,CAAC;gBACN,CAAC;;gBAED,CAAC,EAAE,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;;YAEL,CAAC;QACL", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/reserve/ReserveDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"预约ID\">\r\n{{formData.rid}}</el-form-item>\r\n<el-form-item label=\"科室\">\r\n{{formData.pname}}</el-form-item>\r\n<el-form-item label=\"医生\">\r\n{{formData.by1}}</el-form-item>\r\n<el-form-item label=\"坐诊ID\">\r\n{{formData.plid}}</el-form-item>\r\n<el-form-item label=\"预约日期\">\r\n{{formData.rdate}}</el-form-item>\r\n<el-form-item label=\"预约时间段\">\r\n{{formData.rtime}}</el-form-item>\r\n<el-form-item label=\"挂号费\">\r\n{{formData.pmoney}}</el-form-item>\r\n<el-form-item label=\"用户名\">\r\n{{formData.lname}}</el-form-item>\r\n\r\n<el-form-item label=\"就诊人\">\r\n{{patient.peoname}}\r\n</el-form-item>\r\n<el-form-item label=\"手机号码\">\r\n{{patient.phone}}\r\n</el-form-item>\r\n<el-form-item label=\"性别\">\r\n{{patient.gender}}\r\n</el-form-item>\r\n<el-form-item label=\"年龄\">\r\n{{patient.age}}\r\n</el-form-item>\r\n\r\n\r\n<el-form-item label=\"提交时间\">\r\n{{formData.addtime}}</el-form-item>\r\n<el-form-item label=\"预约状态\">\r\n{{formData.flag}}</el-form-item>\r\n<el-form-item label=\"诊断结果\" prop=\"results\">\r\n<div v-html=\"formData.results\"></div>\r\n</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n        \r\n        import request, { base } from \"../../../../utils/http\";\r\n        export default {\r\n            name: 'ReserveDetail',\r\n            components: {\r\n            },\r\n            data() {\r\n                return {\r\n                    id: '',\r\n                    formData: {}, //表单数据        \r\n                    patient: {}, // 初始化患者对象\r\n        \r\n                };\r\n            },\r\n            created() {\r\n                this.id = this.$route.query.id; //获取参数\r\n                this.getDatas();\r\n            },\r\n        \r\n        \r\n            methods: {\r\n        \r\n                //获取列表数据\r\n                getDatas() {\r\n                    let para = {\r\n                    };\r\n                    this.listLoading = true;\r\n                    let url = base + \"/reserve/get?id=\" + this.id;\r\n                    request.post(url, para).then((res) => {\r\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n                        this.listLoading = false;\r\n\r\n                        let param = {\r\n                            peoid: this.formData.peoid,\r\n                        };\r\n                        let url2 = base + \"/patient/get?id=\" + this.formData.peoid;\r\n                        request.post(url2, param).then((res) => {\r\n                            this.patient = JSON.parse(JSON.stringify(res.resdata));\r\n                        });\r\n                    });\r\n                },\r\n        \r\n                // 返回\r\n                back() {\r\n                    //返回上一页\r\n                    this.$router.go(-1);\r\n                },\r\n        \r\n            },\r\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}