const App = getApp();
const navigationBarHeight = (getApp().diygwstatusBarHeight + 44) + 'px'

// 智谱AI配置
const ZHIPUAI_API = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
const API_KEY = 'cdbe3f5e608394a1353e9ce27397f27b.yOqjVhOmozryAm3f';

Page({
  data: {
    messages: [], // 聊天消息列表
    inputValue: '', // 输入框的值
    loading: false, // 是否正在加载
    lname: '', // 用户名
    userInfo: null, // 用户信息
    member: [],
    url: App.Config.fileBasePath,
    thinking: false, // 是否显示思考中动画
    dots: '', // 思考中的点
    scrollTop: 0, // 滚动位置
  },

  onShareAppMessage: function () { },

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option
      })
    }

    // 获取登录信息
    let userInfo = wx.getStorageSync('lname');
    if (userInfo) {
      this.setData({
        lname: userInfo
      });
    } else {
      wx.redirectTo({
        url: '/pages/login/index'
      });
      return;
    }

    // 添加欢迎消息
    this.setData({
      messages: [{
        role: 'assistant',
        content: '您好！我是智能医疗助手，可以为您提供健康咨询、疾病预防、用药指导等方面的专业建议。请问有什么可以帮助您的吗？',
        time: new Date().toLocaleTimeString()
      }]
    });
  },

  //页面显示
  onShow() {
    this.init()
  },

  async init() {
    this.chushi();
  },

  //初始化用户信息
  chushi() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 1,
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`
    };

    App.HttpService.getData(param, "/users/list2.action?currentPage=1&pageSize=1").then(data => { //执行服务器Servlet
      this.setData({
        member: data.resdata  //把从服务器端得到的值赋值给数组
      });
    });
  },

  //页面跳转
  navigateTo(e) {
    App.navigateTo(e.currentTarget.dataset.url, e.currentTarget.dataset);
  },

  resetForm: function () {
    console.log('form发生了reset事件')
  },

  // 更新思考中动画
  updateThinkingDots() {
    if (!this.data.thinking) return;

    let dots = this.data.dots;
    dots = dots.length >= 3 ? '' : dots + '·';

    this.setData({ dots });
    setTimeout(() => this.updateThinkingDots(), 500);
  },

  // 发送消息
  sendMessage() {
    if (!this.data.inputValue.trim()) {
      return;
    }

    // 添加用户消息
    const userMessage = {
      role: 'user',
      content: this.data.inputValue,
      time: new Date().toLocaleTimeString()
    };

    // 添加思考中消息
    const thinkingMessage = {
      role: 'assistant',
      content: '正在分析',
      time: new Date().toLocaleTimeString(),
      thinking: true
    };

    this.setData({
      messages: [...this.data.messages, userMessage, thinkingMessage],
      inputValue: '',
      loading: true,
      thinking: true,
      dots: ''
    }, () => {
      this.scrollToBottom();
    });

    // 开始思考动画
    this.updateThinkingDots();

    // 调用智谱AI接口
    wx.request({
      url: ZHIPUAI_API,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      data: {
        model: "glm-4-flash",
        messages: [
          {
            role: "system",
            content: "你是一个专业的医疗健康助手，拥有丰富的医学知识。请用专业、温和、易懂的语言为用户提供健康咨询服务。你的职责包括：1.解答健康相关问题 2.提供疾病预防建议 3.给出用药指导建议 4.推荐健康生活方式。重要提醒：你的建议仅供参考，严重疾病请及时就医，不能替代专业医生的诊断和治疗。请用中文回答所有问题。"
          },
          {
            role: "user",
            content: userMessage.content
          }
        ],
        temperature: 0.7,
        max_tokens: 1500
      },
      success: (res) => {
        // 移除思考中消息，添加AI回复消息
        const messages = this.data.messages.slice(0, -1);
        let aiContent = res.data.choices[0].message.content;

        // 在回复末尾添加免责声明
        aiContent += '\n\n💡 温馨提示：以上建议仅供参考，如有严重不适请及时就医。';

        const aiMessage = {
          role: 'assistant',
          content: aiContent,
          time: new Date().toLocaleTimeString()
        };

        this.setData({
          messages: [...messages, aiMessage],
          loading: false,
          thinking: false
        }, () => {
          this.scrollToBottom();
        });
      },
      fail: (err) => {
        console.error('AI回复失败:', err);
        // 移除思考中消息，添加错误消息
        const messages = this.data.messages.slice(0, -1);
        const errorMessage = {
          role: 'assistant',
          content: '抱歉，网络连接出现问题，请稍后重试。如有紧急情况，请及时就医。',
          time: new Date().toLocaleTimeString()
        };

        this.setData({
          messages: [...messages, errorMessage],
          loading: false,
          thinking: false
        });

        wx.showToast({
          title: '网络异常，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 输入框内容改变
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('#message-list').boundingClientRect((rect) => {
        if (rect) {
          const scrollTop = rect.height * 2; // 确保滚动到最底部
          this.setData({
            scrollTop
          });
        }
      }).exec();
    }, 100); // 减少延迟时间以提高响应速度
  }
})

