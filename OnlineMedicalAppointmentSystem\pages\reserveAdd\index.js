const App = getApp();
Page({
  data: {
    msgs1: [],
    msgs2: [],
    patients: [],
    rdate: "",
    rtime: "",
    plid: "",
    week: "",
    selectedPatient: null,
  },

  //弹出提示信息
  showModal(message) {
    App.WxService.showModal({
      title: "友情提示",
      content: message,
      showCancel: !1,
    });
  },
  onShareAppMessage: function () {},

  //页面加载
  onLoad(option) {
    if (option) {
      this.setData({
        globalOption: option,
        rdate: option.rdate,
        rtime: option.rtime,
        week: option.week,
        plid: option.plid,
      });
    }

    this.getForm1();
    this.getPatients();
  },

  //页面显示
  onShow() {
    this.init();
    this.getPatients();
  },

  async init() {
    this.getMsgs1();
  },

  getMsgs1() {
    var that = this;
    //设置要传递的参数
    let param = {
      f: 3,
      did: this.data.globalOption.did,
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/doctor/list2.action?currentPage=1&pageSize=1").then((data) => {
      //执行服务器Servlet
      this.setData({
        msgs1: data.resdata,
      });
    });
  },

  //设置输入验证
  getForm1() {
    this.WxValidate = App.WxValidate({});
  },

  //获取就诊人列表
  getPatients() {
    let param = {
      lname: wx.getStorageSync("lname"),
      loadmsg: `正在加载中`,
    };

    App.HttpService.getData(param, "/patient/list.action?currentPage=1&pageSize=100").then((data) => {
      this.setData({
        patients: data.resdata || [],
        selectedPatient:
          data.resdata && data.resdata.length === 1
            ? data.resdata[0]
            : this.data.selectedPatient,
      });
    });
  },

  //选择就诊人
  selectPatient(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedPatient: this.data.patients[index],
    });
  },

  //跳转到添加就诊人页面
  goToAddPatient() {
    wx.navigateTo({
      url: "/pages/patientAdd/index",
    });
  },

  //添加reserve
  submitForm: function (e) {
    if (!this.data.patients || this.data.patients.length === 0) {
      App.showToast("请先添加就诊人信息", "none");
      return false;
    }

    if (!this.data.selectedPatient) {
      App.showToast("请选择就诊人", "none");
      return false;
    }

    if (!this.WxValidate.checkForm(e)) {
      const error = this.WxValidate.errorList[0];
      App.showToast(error.msg, "none");
      return false;
    }

    var data = e.detail.value;
    data = App.Tools.extend(data, {
      lname: wx.getStorageSync("lname"),
      did: this.data.globalOption.did,
      rdate: this.data.rdate,
      rtime: this.data.rtime,
      week: this.data.week,
      pmoney: this.data.msgs1[0].price,
      pid: this.data.msgs1[0].pid,
      flag: "未支付",
      peoid: this.data.selectedPatient.peoid,
      plid: this.data.plid,
    });

    App.HttpService.saveData(data, "/reserve/add.action").then((result) => {
      if (result.code === 200) {
        App.WxService.showToast({
          title: "提交成功，请尽快支付!",
          icon: "none",
          duration: 1500,
        });

        // 预约成功后，通知医生详情页面更新排班信息
        const pages = getCurrentPages();
        if (pages.length >= 2) {
          const prevPage = pages[pages.length - 2];
          if (prevPage.route === 'pages/doctorView/index') {
            // 方法1：直接更新对应时间段的剩余号数（更快的用户体验）
            prevPage.updateAvailableSlots(data.week, data.rtime, -1);
            // 方法2：重新获取排班信息（确保数据准确性）
            setTimeout(() => {
              prevPage.refreshPlans();
            }, 500);
          }
        }

        setTimeout(function () {
          wx.switchTab({
            url: "../my/index",
          });
        }, 1500);
      } else {
        App.WxService.showToast({
          title: result.msg || "预约失败，请重试",
          icon: "none",
          duration: 2000,
        });
      }
    }).catch((error) => {
      console.error("预约提交失败:", error);
      App.WxService.showToast({
        title: "网络错误，请重试",
        icon: "none",
        duration: 2000,
      });
    });
  },
  resetForm: function () {
    console.log("form发生了reset事件");
  },
});
