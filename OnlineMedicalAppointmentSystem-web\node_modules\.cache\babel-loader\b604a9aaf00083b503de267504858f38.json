{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue?vue&type=template&id=edc10994&scoped=true", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749226188799}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "id", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "$data", "role", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"deznav\" style=\"background:#8BC34A;\">\r\n    <div class=\"deznav-scroll mm-active\">\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '管理员'\">\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">科室管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/partsAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加科室</router-link></li>\r\n            <li><router-link to=\"/partsManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理科室</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/total1\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>科室预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total2\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>医生预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/mailremindertemplateEdit\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 邮件模板设置\r\n              </router-link></li>\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '医生'\">\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n     \r\n            <li><router-link to=\"/plansManage2\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我的排班日历</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">聊天管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/chatinfoManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我收到的聊天</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我收到的挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n   \r\n\r\n                <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n         \r\n              \r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">个人中心</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n               <li><router-link to=\"/doctorInfo\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>修改个人信息</router-link>\r\n            </li>\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n      showexist: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n\r\n    // 修改菜单点击事件\r\n    $('.has-arrow').click(function (e) {\r\n      e.preventDefault();\r\n\r\n      // 为父li元素添加mm-active类\r\n      $(this).parent('li').addClass('mm-active');\r\n\r\n      $(this).next('ul').toggleClass('mm-show');\r\n\r\n      // 关闭其他打开的菜单\r\n      $('.has-arrow').not(this).parent('li').removeClass('mm-active');\r\n      $('.has-arrow').not(this).next('ul').removeClass('mm-show');\r\n    });\r\n\r\n\r\n  },\r\n  methods: {\r\n\r\n    toggleShowExist() {\r\n      this.showexist = !this.showexist;\r\n\r\n      if (this.showexist) {\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      } else {\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"], "mappings": ";;EAEOA,KAAK,EAAC,QAAQ;EAACC,KAA2B,EAA3B;IAAA;EAAA;;;EACbD,KAAK,EAAC;AAAyB;;EAC9BA,KAAK,EAAC,mBAAmB;EAACE,EAAE,EAAC;;;EAoBzB,eAAa,EAAC,OAAO;EAACF,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA0B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA6B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA0B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA6B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA0B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EAgC/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EAcnDD,KAAK,EAAC,mBAAmB;EAACE,EAAE,EAAC;;;EAqBzB,eAAa,EAAC,OAAO;EAACF,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA0B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA2B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA2B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;EA+B/C,eAAa,EAAC,OAAO;EAACD,KAAK,EAAC,kBAAkB;EAACC,KAAQ,EAAR;;;;uBAhV3DE,mBAAA,CA8VM,OA9VNC,UA8VM,GA7VJC,mBAAA,CA4VM,OA5VNC,UA4VM,G,gBA3VJD,mBAAA,CAwMK,MAxMLE,UAwMK,GAvMHF,mBAAA,CAwBK,a,wsDALHA,mBAAA,CAIK,MAJLG,UAIK,GAHHH,mBAAA,CACK,aADDI,YAAA,CAAoGC,sBAAA;IAAvFC,EAAE,EAAC,cAAc;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;YAM9FK,mBAAA,CAyBK,a,wsDANHA,mBAAA,CAKK,MALLQ,UAKK,GAJHR,mBAAA,CAA0G,aAAtGI,YAAA,CAAiGC,sBAAA;IAApFC,EAAE,EAAC,WAAW;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;QACvFK,mBAAA,CACK,aADDI,YAAA,CAAoGC,sBAAA;IAAvFC,EAAE,EAAC,cAAc;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;YAQ9FK,mBAAA,CAwBK,a,wsDALHA,mBAAA,CAIK,MAJLS,UAIK,GAHHT,mBAAA,CAA2G,aAAvGI,YAAA,CAAkGC,sBAAA;IAArFC,EAAE,EAAC,YAAY;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;QACxFK,mBAAA,CACK,aADDI,YAAA,CAAqGC,sBAAA;IAAxFC,EAAE,EAAC,eAAe;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;YAK/FK,mBAAA,CAyBK,a,0sDANHA,mBAAA,CAKK,MALLU,UAKK,GAJHV,mBAAA,CAA0G,aAAtGI,YAAA,CAAiGC,sBAAA;IAApFC,EAAE,EAAC,WAAW;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;QACvFK,mBAAA,CACK,aADDI,YAAA,CAAoGC,sBAAA;IAAvFC,EAAE,EAAC,cAAc;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,QAAAA,MAAA,OAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,MAAI,E;;;YAQ9FK,mBAAA,CAwBK,a,4sDALHA,mBAAA,CAIK,MAJLW,UAIK,GAHHX,mBAAA,CACK,aADDI,YAAA,CAAwGC,sBAAA;IAA3FC,EAAE,EAAC,gBAAgB;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;YAMlGK,mBAAA,CA8BK,a,0sDAXHA,mBAAA,CAUK,MAVLY,UAUK,GATHZ,mBAAA,CACK,aADDI,YAAA,CAAiGC,sBAAA;IAApFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;QAEjFK,mBAAA,CACD,aADKI,YAAA,CAAiGC,sBAAA;IAApFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;QAEvFK,mBAAA,CACD,aADKI,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,OAAK,E;;;QAEtFK,mBAAA,CACD,aADKI,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,OAAK,E;;;YAMhGK,mBAAA,CA2BK,a,0sDARHA,mBAAA,CAOK,MAPLa,WAOK,GANHb,mBAAA,CAEqB,aAFjBI,YAAA,CAEYC,sBAAA;IAFCC,EAAE,EAAC,2BAA2B;IAACX,KAAK,EAAC;;sBAClD,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,UAC/B,E;;;QACFK,mBAAA,CAEqB,aAFjBI,YAAA,CAEYC,sBAAA;IAFCC,EAAE,EAAC,WAAW;IAACX,KAAK,EAAC;;sBAClC,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAC/B,E;;;8CAlMwCmB,KAAA,CAAAC,IAAI,Y,mBA0MpDf,mBAAA,CA+IK,MA/ILgB,WA+IK,GA7IHhB,mBAAA,CAyBK,a,0sDANHA,mBAAA,CAKK,MALLiB,WAKK,GAHHjB,mBAAA,CACK,aADDI,YAAA,CAAuGC,sBAAA;IAA1FC,EAAE,EAAC,eAAe;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;YAKjGK,mBAAA,CAwBK,a,0sDALHA,mBAAA,CAIK,MAJLkB,WAIK,GAHHlB,mBAAA,CACK,aADDI,YAAA,CAAyGC,sBAAA;IAA5FC,EAAE,EAAC,iBAAiB;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;YAOnGK,mBAAA,CAwBK,a,4sDALHA,mBAAA,CAIK,MAJLmB,WAIK,GAHHnB,mBAAA,CACK,aADDI,YAAA,CAAwGC,sBAAA;IAA3FC,EAAE,EAAC,gBAAgB;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;YAO1FK,mBAAA,CA4BH,a,0sDATHA,mBAAA,CAQK,MARLoB,WAQK,GALGpB,mBAAA,CACD,aADKI,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,OAAK,E;;;QAEtFK,mBAAA,CACD,aADKI,YAAA,CAAgGC,sBAAA;IAAnFC,EAAE,EAAC,SAAS;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,OAAK,E;;;YAOhGK,mBAAA,CA0BK,a,0sDAPHA,mBAAA,CAMK,MANLqB,WAMK,GALArB,mBAAA,CACE,aADEI,YAAA,CAAqGC,sBAAA;IAAxFC,EAAE,EAAC,aAAa;IAACX,KAAK,EAAC;;sBAAgB,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAAM,E;;;QAE9FK,mBAAA,CAEqB,aAFjBI,YAAA,CAEYC,sBAAA;IAFCC,EAAE,EAAC,WAAW;IAACX,KAAK,EAAC;;sBAClC,MAA6BY,MAAA,SAAAA,MAAA,QAA7BP,mBAAA,CAA6B;MAA1BL,KAAK,EAAC;IAAe,4B,iBAAK,QAC/B,E;;;8CAzIwCmB,KAAA,CAAAC,IAAI,W", "ignoreList": []}]}