{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue", "mtime": 1749226248780}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "name", "components", "data", "page", "currentPage", "pageSize", "totalCount", "isClear", "listLoading", "btnLoading", "datalist", "created", "getDatas", "methods", "handleDelete", "index", "row", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "url", "cid", "post", "res", "$message", "message", "offset", "catch", "handleCurrentChange", "val", "user", "JSON", "parse", "sessionStorage", "getItem", "para", "did", "condition", "resdata", "length", "isPage", "count", "error", "console", "handleShow", "$router", "push", "path", "query", "id", "handleEdit"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\chatinfo\\ChatinfoManage.vue"], "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n      \n<el-table :data=\"datalist\" border stripe style=\"width: 100%\"  v-loading=\"listLoading\"   highlight-current-row   max-height=\"600\"     size=\"small\">\n<el-table-column prop=\"lname\" label=\"用户名\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"content\" label=\"聊天内容\"  align=\"center\"></el-table-column>\n<el-table-column prop=\"sendtime\" label=\"发送时间\"  align=\"center\"></el-table-column>\n<el-table-column label=\"操作\" min-width=\"200\" align=\"center\">\n<template #default=\"scope\">\n<el-button type=\"danger\" size=\"mini\" @click=\"handleDelete(scope.$index, scope.row)\" icon=\"el-icon-delete\" style=\" padding: 3px 6px 3px 6px;\">删除</el-button>\n</template>\n</el-table-column>\n</el-table>\n<el-pagination  @current-change=\"handleCurrentChange\" :current-page=\"page.currentPage\" :page-size=\"page.pageSize\" \n background layout=\"total, prev, pager, next, jumper\" :total=\"page.totalCount\" \n style=\"float: right; margin: 10px 20px 0 0\"></el-pagination>\n\n    </div>\n</template>\n\n<script>\nimport request, { base } from \"../../../../utils/http\";\nexport default {\n  name: 'chatinfo',\n  components: {\n    \n  },  \n    data() {\n      return {\n       \n        page: {\n          currentPage: 1, // 当前页\n          pageSize: 10, // 每页显示条目个数\n          totalCount: 0, // 总条目数\n        },\n        isClear: false,      \n        \n        listLoading: false, //列表加载状态\n        btnLoading: false, //保存按钮加载状态\n        datalist: [], //表格数据  \n    \n      };\n    },\n    created() {\n      this.getDatas();\n    },\n\n \n    methods: {    \n\n              \n       // 删除聊天\n        handleDelete(index, row) {\n          this.$confirm(\"确认删除该记录吗?\", \"提示\", {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          })\n            .then(() => {\n              this.listLoading = true;\n              let url = base + \"/chatinfo/del?id=\" + row.cid;\n              request.post(url).then((res) => {\n                this.listLoading = false;\n             \n                this.$message({\n                  message: \"删除成功\",\n                  type: \"success\",\n                  offset: 320,\n                });\n                this.getDatas();\n              });\n            })\n            .catch(() => { });\n        },\n                \n        // 分页\n        handleCurrentChange(val) {\n          this.page.currentPage = val;\n          this.getDatas();\n        },     \n     \n        //获取列表数据\n      getDatas() {\n           var user = JSON.parse(sessionStorage.getItem('user')); //获取用户信息\n        let para = {\n            did:user.did,\n            condition:\" and a.cidi \"\n          };\n          this.listLoading = true;\n          let url = base + \"/chatinfo/list?currentPage=\" + this.page.currentPage+ \"&pageSize=\" + this.page.pageSize;\n          request.post(url, para).then((res) => {\n            if (res.resdata.length > 0) {\n              this.isPage = true;\n            } else {\n              this.isPage = false;\n            }\n            this.page.totalCount = res.count;\n            this.datalist = res.resdata;\n            this.listLoading = false;\n          }).catch((error) => {\n            console.error('获取聊天信息列表失败:', error);\n            this.listLoading = false;\n            this.$message.error('获取聊天信息列表失败');\n          });\n        },\n        \n           \n        // 查看\n        handleShow(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoDetail\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n    \n        // 编辑\n        handleEdit(index, row) {\n          this.$router.push({\n            path: \"/ChatinfoEdit\",\n             query: {\n                id: row.cid,\n              },\n          });\n        },\n      },\n}\n\n</script>\n<style scoped>\n</style>\n \n\n"], "mappings": ";AAqBA,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,CAEZ,CAAC;EACCC,IAAIA,CAAA,EAAG;IACL,OAAO;MAELC,IAAI,EAAE;QACJC,WAAW,EAAE,CAAC;QAAE;QAChBC,QAAQ,EAAE,EAAE;QAAE;QACdC,UAAU,EAAE,CAAC,CAAE;MACjB,CAAC;MACDC,OAAO,EAAE,KAAK;MAEdC,WAAW,EAAE,KAAK;MAAE;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,EAAE,CAAE;IAEhB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,QAAQ,CAAC,CAAC;EACjB,CAAC;EAGDC,OAAO,EAAE;IAGN;IACCC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;MACvB,IAAI,CAACC,QAAQ,CAAC,WAAW,EAAE,IAAI,EAAE;QAC/BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACV,IAAI,CAACb,WAAU,GAAI,IAAI;QACvB,IAAIc,GAAE,GAAIvB,IAAG,GAAI,mBAAkB,GAAIiB,GAAG,CAACO,GAAG;QAC9CzB,OAAO,CAAC0B,IAAI,CAACF,GAAG,CAAC,CAACD,IAAI,CAAEI,GAAG,IAAK;UAC9B,IAAI,CAACjB,WAAU,GAAI,KAAK;UAExB,IAAI,CAACkB,QAAQ,CAAC;YACZC,OAAO,EAAE,MAAM;YACfP,IAAI,EAAE,SAAS;YACfQ,MAAM,EAAE;UACV,CAAC,CAAC;UACF,IAAI,CAAChB,QAAQ,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EACAiB,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB,CAAC;IAED;IACAC,mBAAmBA,CAACC,GAAG,EAAE;MACvB,IAAI,CAAC5B,IAAI,CAACC,WAAU,GAAI2B,GAAG;MAC3B,IAAI,CAACnB,QAAQ,CAAC,CAAC;IACjB,CAAC;IAED;IACFA,QAAQA,CAAA,EAAG;MACN,IAAIoB,IAAG,GAAIC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;MAC1D,IAAIC,IAAG,GAAI;QACPC,GAAG,EAACN,IAAI,CAACM,GAAG;QACZC,SAAS,EAAC;MACZ,CAAC;MACD,IAAI,CAAC/B,WAAU,GAAI,IAAI;MACvB,IAAIc,GAAE,GAAIvB,IAAG,GAAI,6BAA4B,GAAI,IAAI,CAACI,IAAI,CAACC,WAAW,GAAE,YAAW,GAAI,IAAI,CAACD,IAAI,CAACE,QAAQ;MACzGP,OAAO,CAAC0B,IAAI,CAACF,GAAG,EAAEe,IAAI,CAAC,CAAChB,IAAI,CAAEI,GAAG,IAAK;QACpC,IAAIA,GAAG,CAACe,OAAO,CAACC,MAAK,GAAI,CAAC,EAAE;UAC1B,IAAI,CAACC,MAAK,GAAI,IAAI;QACpB,OAAO;UACL,IAAI,CAACA,MAAK,GAAI,KAAK;QACrB;QACA,IAAI,CAACvC,IAAI,CAACG,UAAS,GAAImB,GAAG,CAACkB,KAAK;QAChC,IAAI,CAACjC,QAAO,GAAIe,GAAG,CAACe,OAAO;QAC3B,IAAI,CAAChC,WAAU,GAAI,KAAK;MAC1B,CAAC,CAAC,CAACqB,KAAK,CAAEe,KAAK,IAAK;QAClBC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC,IAAI,CAACpC,WAAU,GAAI,KAAK;QACxB,IAAI,CAACkB,QAAQ,CAACkB,KAAK,CAAC,YAAY,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC;IAGD;IACAE,UAAUA,CAAC/B,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,iBAAiB;QACtBC,KAAK,EAAE;UACJC,EAAE,EAAEnC,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ,CAAC;IAED;IACA6B,UAAUA,CAACrC,KAAK,EAAEC,GAAG,EAAE;MACrB,IAAI,CAAC+B,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE,eAAe;QACpBC,KAAK,EAAE;UACJC,EAAE,EAAEnC,GAAG,CAACO;QACV;MACJ,CAAC,CAAC;IACJ;EACF;AACN", "ignoreList": []}]}