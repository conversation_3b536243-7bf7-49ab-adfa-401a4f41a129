<view class="container">
    <diy-navbar bgColor="green" isBack="{{false}}">
        <view slot="backText"> 返回 </view>
        <view slot="content"> AI专家 </view>
    </diy-navbar>
    <!-- 消息列表 -->
    <scroll-view class="message-list" id="message-list" scroll-y scroll-top="{{scrollTop}}" scroll-with-animation
        enhanced show-scrollbar="{{false}}">
        <view class="message {{item.role === 'user' ? 'message-user' : 'message-ai'}}" wx:for="{{messages}}"
            wx:key="index">
            <!-- 头像 -->
            <view class="avatar">
                <image src="{{item.role === 'user' ? url+member[0].pic : '/assets/images/icon1_rzyh.png'}}"
                    mode="aspectFill"></image>
            </view>
            <!-- 消息内容 -->
            <view class="message-content">
                <view class="name">{{item.role === 'user' ? '我' : '医疗专家'}}</view>
                <view class="content22 {{item.thinking ? 'thinking' : ''}}">
                    {{item.content}}{{item.thinking ? dots : ''}}
                </view>
                <view class="time">{{item.time}}</view>
            </view>
        </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
        <view class="input-box">
            <input type="text" placeholder="请输入您的健康问题" value="{{inputValue}}" bindinput="onInput" disabled="{{loading}}"
                bindconfirm="sendMessage" confirm-type="send" />
            <button class="send-btn {{loading ? 'loading' : ''}}" bindtap="sendMessage"
                disabled="{{loading || !inputValue}}">
                {{loading ? '分析中...' : '发送'}}
            </button>
        </view>
    </view>
</view>