package com.model;

/**
* (plans)坐诊安排实体类
*/
public class Plans extends ComData{
	
	private static final long serialVersionUID = 216136838688583L;
	private Integer plid;    //ID
	private Integer did;    //医生
	private String dname;
	private String weeks;    //星期
	private String ptime;    //时间段
	private Integer people;    //号数	
	private Integer yylatenum; // 预约号数

	public Integer getPlid() {
		return plid;
	}

	public void setPlid(Integer plid) {
		this.plid = plid;
	}

	public Integer getDid() {
		return did;
	}

	public void setDid(Integer did) {
		this.did = did;
	}

	public String getDname() {
		return dname;
	}

	public void setDname(String dname) {
		this.dname = dname;
	}

	public String getWeeks() {
		return weeks;
	}

	public void setWeeks(String weeks) {
		this.weeks = weeks;
	}

	public String getPtime() {
		return ptime;
	}

	public void setPtime(String ptime) {
		this.ptime = ptime;
	}

	public Integer getPeople() {
		return people;
	}

	public void setPeople(Integer people) {
		this.people = people;
	}

	public Integer getYylatenum() {
		return yylatenum;
	}

	public void setYylatenum(Integer yylatenum) {
		this.yylatenum = yylatenum;
	}


}

