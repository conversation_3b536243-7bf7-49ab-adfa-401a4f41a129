<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel导出功能测试</title>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Excel导出功能测试</h1>
    <button onclick="testExcelExport()">测试Excel导出</button>
    
    <script>
        function testExcelExport() {
            try {
                // 测试数据
                const testData = [
                    { '序号': 1, '科室名称': '内科', '预约数量': 25, '占比': '35.7%' },
                    { '序号': 2, '科室名称': '外科', '预约数量': 18, '占比': '25.7%' },
                    { '序号': 3, '科室名称': '儿科', '预约数量': 15, '占比': '21.4%' },
                    { '序号': 4, '科室名称': '妇科', '预约数量': 12, '占比': '17.1%' }
                ];

                // 创建工作簿
                const ws = XLSX.utils.json_to_sheet(testData);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "科室预约统计测试");

                // 设置列宽
                ws['!cols'] = [
                    { wch: 8 },  // 序号
                    { wch: 20 }, // 科室名称
                    { wch: 12 }, // 预约数量
                    { wch: 12 }  // 占比
                ];

                // 导出文件
                const fileName = `科室预约统计测试_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
                XLSX.writeFile(wb, fileName);
                
                alert('Excel导出测试成功！');
            } catch (error) {
                console.error('Excel导出测试失败:', error);
                alert('Excel导出测试失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
