package com.util;

/**
 * 数字转换工具类
 * 用于安全地将各种类型的数据转换为整数
 */
public class NumberUtils {
    
    /**
     * 安全地将对象转换为整数
     * 支持Integer、String、Double等类型
     * 
     * @param obj 要转换的对象
     * @return 转换后的整数值
     * @throws NumberFormatException 如果无法转换为有效的整数
     */
    public static Integer safeParseInt(Object obj) {
        if (obj == null) {
            return null;
        }
        
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        
        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }
        
        String str = obj.toString().trim();
        if (str.isEmpty()) {
            return null;
        }
        
        try {
            // 先尝试直接解析为整数
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            try {
                // 如果失败，可能是浮点数格式，先转为Double再转为Integer
                return Double.valueOf(str).intValue();
            } catch (NumberFormatException e2) {
                throw new NumberFormatException("Cannot parse '" + str + "' as integer");
            }
        }
    }
    
    /**
     * 安全地将对象转换为整数，提供默认值
     * 
     * @param obj 要转换的对象
     * @param defaultValue 转换失败时的默认值
     * @return 转换后的整数值或默认值
     */
    public static Integer safeParseInt(Object obj, Integer defaultValue) {
        try {
            Integer result = safeParseInt(obj);
            return result != null ? result : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
}
