.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f5f7fa;
}

.message-list {
    flex: 1;
    padding: 20rpx;
    overflow-y: auto;
}

.message {
    display: flex;
    margin-bottom: 30rpx;
    opacity: 0;
    transform: translateY(20rpx);
    animation: fadeInUp 0.3s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20rpx);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-user {
    flex-direction: row-reverse;
}

.avatar {
    width: 80rpx;
    height: 80rpx;
    margin: 0 20rpx;
    border-radius: 50%;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.avatar image {
    width: 100%;
    height: 100%;
}

.message-content {
    max-width: 70%;
}

.message-user .message-content {
    align-items: flex-end;
}

.name {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 10rpx;
}

.content22 {
    padding: 20rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.6;
    word-break: break-all;
    position: relative;
    transition: all 0.3s ease;
}

.message-ai .content {
    background: #fff;
    color: #333;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid #e8f4fd;
}

.message-user .content {
    background: linear-gradient(135deg, #07c160, #06ad56);
    color: #fff;
    box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.content.thinking {
    background: #f0f9ff;
    color: #1890ff;
    border: 1rpx solid #91d5ff;
    animation: thinking 1.5s infinite;
}

.content.thinking::after {
    content: '';
    position: absolute;
    bottom: -10rpx;
    left: 20rpx;
    width: 20rpx;
    height: 20rpx;
    background: #f0f9ff;
    border: 1rpx solid #91d5ff;
    border-top: none;
    border-right: none;
    transform: rotate(45deg);
}

.time {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
}

.input-area {
    padding: 20rpx;
    background: #fff;
    border-top: 1rpx solid #eee;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.input-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 40rpx;
    padding: 10rpx 20rpx;
    transition: all 0.3s ease;
    border: 2rpx solid transparent;
}

.input-box.focus {
    border-color: #07c160;
    box-shadow: 0 0 0 6rpx rgba(7, 193, 96, 0.1);
}

.input-box input {
    flex: 1;
    height: 70rpx;
    font-size: 28rpx;
    padding: 0 20rpx;
    background: transparent;
}

.send-btn {
    width: 140rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    background: linear-gradient(135deg, #07c160, #06ad56);
    color: #fff;
    border-radius: 35rpx;
    font-size: 28rpx;
    margin: 0;
    padding: 0;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.send-btn.loading {
    background: #95de64;
    animation: pulse 1.5s infinite;
}

.send-btn[disabled] {
    background: #d9d9d9;
    color: #bfbfbf !important;
    box-shadow: none;
}

@keyframes thinking {

    0%,
    100% {
        opacity: 0.8;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(0.98);
    }
}