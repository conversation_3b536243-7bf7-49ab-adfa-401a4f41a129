﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
        <!-- 操作按钮 -->
        <div style="margin-bottom: 20px; text-align: right;">
            <el-button type="primary" @click="exportToExcel" :loading="exportLoading">
                <i class="el-icon-download"></i> 导出Excel
            </el-button>
        </div>

        <!-- 图表容器 -->
        <div class="echart" id="mychart" :style="myChartStyle"></div>
    </div>
</template>

<script>

 import request, { base } from "../../../../utils/http";
 import * as echarts from "echarts";
 import * as XLSX from 'xlsx';

 export default {
   data() {
     return {
       myChart: {},
       pieData: [],
       pieName: [],
       exportLoading: false,
       myChartStyle: { float: "left", width: "100%", height: "550px" } //图表样式
     };
   },
   mounted() {
     this.getdata();
   },
   methods: {
 
     //数据初始化
     getdata() {
       let url = base + "/ReportData/queryReport";

       let para = {};

       request.post(url, para).then((res) => {
         if (res.code == 200) {
           var ss = res.resdata;
           this.pieData = [];

           for (let i = 0; i < ss.length; i++) {
             this.pieData[i] = {
               name: ss[i].name,
               value: ss[i].num
             };
           }

           this.initDate();
           this.initEcharts();
         } else {
           this.$message.error(res.msg);
         }
       }).catch((error) => {
         console.error('获取数据失败:', error);
         this.$message.error('获取数据失败');
       });
     },
 
 
     initDate() {
       for (let i = 0; i < this.pieData.length; i++) {
         this.pieName[i] = this.pieData[i].name;
       }
     },
     initEcharts() {
       // 饼图
       const option = {
         legend: {
           // 图例
           data: this.pieName,
           right: "10%",
           top: "10%",
           orient: "vertical"
         },
         title: {
           text: "科室预约统计",
           top: "10%",
           left: "center"
         },
         tooltip: {
           trigger: 'item',
           formatter: '{a} <br/>{b}: {c} ({d}%)'
         },
         series: [
           {
             name: '预约数量',
             type: "pie",
             label: {
               show: true,
               formatter: "{b} : {c} ({d}%)" // b代表名称，c代表对应值，d代表百分比
             },
             radius: "50%", //饼图半径
             center: ['40%', '50%'],
             data: this.pieData,
             emphasis: {
               itemStyle: {
                 shadowBlur: 10,
                 shadowOffsetX: 0,
                 shadowColor: 'rgba(0, 0, 0, 0.5)'
               }
             }
           }
         ]
       };

       this.myChart = echarts.init(document.getElementById("mychart"));
       this.myChart.setOption(option);
       //随着屏幕大小调节图表
       window.addEventListener("resize", () => {
         this.myChart.resize();
       });
     },

     // Excel导出功能
     exportToExcel() {
       this.exportLoading = true;

       try {
         // 准备导出数据
         const exportData = this.pieData.map((item, index) => ({
           '序号': index + 1,
           '科室名称': item.name,
           '预约数量': item.value,
           '占比': this.pieData.length > 0 ? ((item.value / this.pieData.reduce((sum, data) => sum + data.value, 0)) * 100).toFixed(2) + '%' : '0%'
         }));

         // 创建工作簿
         const ws = XLSX.utils.json_to_sheet(exportData);
         const wb = XLSX.utils.book_new();
         XLSX.utils.book_append_sheet(wb, ws, "科室预约统计");

         // 设置列宽
         ws['!cols'] = [
           { wch: 8 },  // 序号
           { wch: 20 }, // 科室名称
           { wch: 12 }, // 预约数量
           { wch: 12 }  // 占比
         ];

         // 导出文件
         const fileName = `科室预约统计_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
         XLSX.writeFile(wb, fileName);

         this.$message.success('导出成功！');
       } catch (error) {
         console.error('导出失败:', error);
         this.$message.error('导出失败，请重试');
       } finally {
         this.exportLoading = false;
       }
     }
   }
 };
</script>
<style scoped>
</style>
 

