{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue", "mtime": 1749225960566}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\Home.vue"], "names": [], "mappings": ";AAoGA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/B;IACF,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC5B,CAAC;QACH,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;;QAE/B;;MAEF,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;IACF;EACF;AACF,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%; padding: 20px;\" id=\"home\">\r\n    <!-- 欢迎信息 -->\r\n    <div style=\"text-align: center; margin-bottom: 30px;\">\r\n      <h2 style=\"color: #409EFF; margin-bottom: 10px;\">欢迎使用医院挂号预约系统</h2>\r\n      <p style=\"font-size: 16px; color: #666;\">\r\n        账号：<b style=\"color: #E6A23C;\">{{ userLname }}</b>，\r\n        身份：<b style=\"color: #E6A23C;\">{{ role }}</b>\r\n      </p>\r\n    </div>\r\n\r\n    <!-- 统计卡片 -->\r\n    <div v-loading=\"loading\" style=\"display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;\">\r\n      <!-- 管理员统计卡片 -->\r\n      <template v-if=\"role === '管理员'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon patients\">\r\n              <i class=\"el-icon-user\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.patientCount || 0 }}</h3>\r\n              <p>患者总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon doctors\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorCount || 0 }}</h3>\r\n              <p>医生总数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.todayAppointmentCount || 0 }}</h3>\r\n              <p>今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.totalAppointmentCount || 0 }}</h3>\r\n              <p>总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n\r\n      <!-- 医生统计卡片 -->\r\n      <template v-if=\"role === '医生'\">\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon today-appointments\">\r\n              <i class=\"el-icon-date\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTodayAppointmentCount || 0 }}</h3>\r\n              <p>今日挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n\r\n        <el-card class=\"stat-card\" shadow=\"hover\">\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-icon total-appointments\">\r\n              <i class=\"el-icon-document\"></i>\r\n            </div>\r\n            <div class=\"stat-info\">\r\n              <h3>{{ statistics.doctorTotalAppointmentCount || 0 }}</h3>\r\n              <p>我的总挂号数</p>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n      </template>\r\n    </div>\r\n\r\n    <!-- 操作提示 -->\r\n    <div style=\"text-align: center; margin-top: 40px; color: #909399;\">\r\n      <p style=\"font-size: 14px;\">请在左侧菜单中选择您要进行的操作！</p>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request, { base } from \"../../../utils/http\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      loading: false,\r\n      statistics: {\r\n        patientCount: 0,\r\n        doctorCount: 0,\r\n        todayAppointmentCount: 0,\r\n        totalAppointmentCount: 0,\r\n        doctorTodayAppointmentCount: 0,\r\n        doctorTotalAppointmentCount: 0\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n    this.loadStatistics();\r\n  },\r\n  methods: {\r\n    // 加载统计数据\r\n    async loadStatistics() {\r\n      this.loading = true;\r\n      try {\r\n        const user = JSON.parse(sessionStorage.getItem(\"user\"));\r\n        let url = base + \"/statistics/dashboard\";\r\n\r\n        const params = {\r\n          role: this.role\r\n        };\r\n\r\n        // 如果是医生，传递医生ID\r\n        if (this.role === '医生' && user) {\r\n          params.doctorId = user.did;\r\n        }\r\n\r\n        const response = await request.post(url, params);\r\n        if (response.code === 200) {\r\n          this.statistics = response.resdata;\r\n        } else {\r\n          this.$message.error('获取统计数据失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('加载统计数据失败:', error);\r\n        // 如果后端服务器未启动，显示模拟数据用于演示\r\n        if (this.role === '管理员') {\r\n          this.statistics = {\r\n            patientCount: 156,\r\n            doctorCount: 23,\r\n            todayAppointmentCount: 45,\r\n            totalAppointmentCount: 1234\r\n          };\r\n        } else if (this.role === '医生') {\r\n         \r\n        }\r\n     \r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.stat-card {\r\n  width: 230px;\r\n  margin-bottom: 20px;\r\n  border-radius: 8px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-5px);\r\n}\r\n\r\n.stat-content {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n}\r\n\r\n.stat-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  font-size: 24px;\r\n  color: white;\r\n}\r\n\r\n.stat-icon.patients {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n}\r\n\r\n.stat-icon.doctors {\r\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n}\r\n\r\n.stat-icon.today-appointments {\r\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\r\n}\r\n\r\n.stat-icon.total-appointments {\r\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\r\n}\r\n\r\n.stat-info h3 {\r\n  font-size: 32px;\r\n  font-weight: bold;\r\n  margin: 0;\r\n  color: #303133;\r\n}\r\n\r\n.stat-info p {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin: 5px 0 0 0;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .stat-card {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}