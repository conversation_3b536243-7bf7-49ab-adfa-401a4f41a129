﻿<template>
    <div style="width: 100%;line-height: 30px;text-align: left;">
       <el-form :model="formData" label-width="20%" align="left">
<el-form-item label="预约ID">
{{formData.rid}}</el-form-item>
<el-form-item label="科室">
{{formData.pname}}</el-form-item>
<el-form-item label="医生">
{{formData.by1}}</el-form-item>
<el-form-item label="坐诊ID">
{{formData.plid}}</el-form-item>
<el-form-item label="预约日期">
{{formData.rdate}}</el-form-item>
<el-form-item label="预约时间段">
{{formData.rtime}}</el-form-item>
<el-form-item label="挂号费">
{{formData.pmoney}}</el-form-item>
<el-form-item label="用户名">
{{formData.lname}}</el-form-item>

<el-form-item label="就诊人">
{{patient.peoname}}
</el-form-item>
<el-form-item label="手机号码">
{{patient.phone}}
</el-form-item>
<el-form-item label="性别">
{{patient.gender}}
</el-form-item>
<el-form-item label="年龄">
{{patient.age}}
</el-form-item>


<el-form-item label="提交时间">
{{formData.addtime}}</el-form-item>
<el-form-item label="预约状态">
{{formData.flag}}</el-form-item>
<el-form-item label="诊断结果" prop="results">
<div v-html="formData.results"></div>
</el-form-item>
<el-form-item>
<el-button type="info" size="small" @click="back" icon="el-icon-back">返 回</el-button>
</el-form-item>
</el-form>


    </div>
</template>

<script>
        
        import request, { base } from "../../../../utils/http";
        export default {
            name: 'ReserveDetail',
            components: {
            },
            data() {
                return {
                    id: '',
                    formData: {}, //表单数据        
                    patient: {}, // 初始化患者对象
        
                };
            },
            created() {
                this.id = this.$route.query.id; //获取参数
                this.getDatas();
            },
        
        
            methods: {
        
                //获取列表数据
                getDatas() {
                    let para = {
                    };
                    this.listLoading = true;
                    let url = base + "/reserve/get?id=" + this.id;
                    request.post(url, para).then((res) => {
                        this.formData = JSON.parse(JSON.stringify(res.resdata));
                        this.listLoading = false;

                        let param = {
                            peoid: this.formData.peoid,
                        };
                        let url2 = base + "/patient/get?id=" + this.formData.peoid;
                        request.post(url2, param).then((res) => {
                            this.patient = JSON.parse(JSON.stringify(res.resdata));
                        });
                    });
                },
        
                // 返回
                back() {
                    //返回上一页
                    this.$router.go(-1);
                },
        
            },
        }

</script>
<style scoped>
</style>
 

