{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total4.vue?vue&type=template&id=76165f95", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total4.vue", "mtime": 1749222223568}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1749193687394}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CiAgICA8ZGl2IHN0eWxlPSJ3aWR0aDogMTAwJTtsaW5lLWhlaWdodDogMzBweDt0ZXh0LWFsaWduOiBsZWZ0OyI+CiAgICAgICAgPCEtLSDmk43kvZzmjInpkq4gLS0+CiAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLWJvdHRvbTogMjBweDsgdGV4dC1hbGlnbjogcmlnaHQ7Ij4KICAgICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImV4cG9ydFRvRXhjZWwiIDpsb2FkaW5nPSJleHBvcnRMb2FkaW5nIj4KICAgICAgICAgICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRvd25sb2FkIj48L2k+IOWvvOWHukV4Y2VsCiAgICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgIDwvZGl2PgoKICAgICAgICA8IS0tIOWbvuihqOWuueWZqCAtLT4KICAgICAgICA8ZGl2IGNsYXNzPSJlY2hhcnQiIGlkPSJteWNoYXJ0IiA6c3R5bGU9Im15Q2hhcnRTdHlsZSI+PC9kaXY+CiAgICA8L2Rpdj4K"}, {"version": 3, "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\views\\admin\\total\\Total4.vue"], "names": [], "mappings": ";IACI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "I:/product4/00452HospitalRegistrationBooking/OnlineMedicalAppointmentSystem-web/src/views/admin/total/Total4.vue", "sourceRoot": "", "sourcesContent": ["<template>\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\n        <!-- 操作按钮 -->\n        <div style=\"margin-bottom: 20px; text-align: right;\">\n            <el-button type=\"primary\" @click=\"exportToExcel\" :loading=\"exportLoading\">\n                <i class=\"el-icon-download\"></i> 导出Excel\n            </el-button>\n        </div>\n\n        <!-- 图表容器 -->\n        <div class=\"echart\" id=\"mychart\" :style=\"myChartStyle\"></div>\n    </div>\n</template>\n\n<script>\n\n import * as echarts from \"echarts\";\n import request, { base } from \"../../../../utils/http\";\n import * as XLSX from 'xlsx';\n\n export default {\n   data() {\n     return {\n       myChartStyle: {\n         height: \"500px\",\n         width: \"100%\"\n       },\n       pieData: [],\n       pieName: [],\n       exportLoading: false,\n       myChart: null,\n       role: '',\n       doctorId: null\n     };\n   },\n   mounted() {\n     // 获取用户角色和ID\n     this.role = sessionStorage.getItem(\"role\");\n     const user = JSON.parse(sessionStorage.getItem(\"user\") || '{}');\n     if (this.role === '医生' && user.did) {\n       this.doctorId = user.did;\n     }\n\n     this.getdata();\n   },\n   methods: {\n \n     //数据初始化\n     getdata() {\n       let url = base + \"/ReportData/queryReport4\";\n\n       let para = {};\n\n       // 如果是医生，只查看自己的数据\n       if (this.role === '医生' && this.doctorId) {\n         para.by1 = this.doctorId.toString();\n       }\n\n       request.post(url, para).then((res) => {\n         if (res.code == 200) {\n           var ss = res.resdata;\n           var pieName2 = [];\n           var pieData2 = [];\n           for (let i = 0; i < ss.length; i++) {\n             pieName2[i] = ss[i].name;\n             pieData2[i] = ss[i].num;\n           }\n           this.pieName = pieName2;\n           this.pieData = pieData2;\n           this.initEcharts();\n         } else {\n           this.$message.error(res.msg);\n         }\n       }).catch((error) => {\n         console.error('获取数据失败:', error);\n         this.$message.error('获取数据失败');\n       });\n     },\n \n     initEcharts() {\n       const chartDom = document.getElementById(\"mychart\");\n       this.myChart = echarts.init(chartDom);\n\n       const title = this.role === '医生' ? '我的月预约统计' : '月预约统计';\n\n       const option = {\n         title: {\n           text: title,\n           left: 'center'\n         },\n         xAxis: {\n           type: \"category\",\n           data: this.pieName,\n           name: '月份'\n         },\n         tooltip: {\n           trigger: 'axis',\n           axisPointer: {\n             type: 'shadow'\n           },\n           formatter: '{a} <br/>{b}: {c}'\n         },\n         yAxis: {\n           type: \"value\",\n           name: '预约数量'\n         },\n         series: [\n           {\n             name: '预约数量',\n             data: this.pieData,\n             type: \"bar\",\n             itemStyle: {\n               color: '#67C23A'\n             }\n           }\n         ]\n       };\n       this.myChart.setOption(option);\n\n       //随着屏幕大小调节图表\n       window.addEventListener(\"resize\", () => {\n         if (this.myChart) {\n           this.myChart.resize();\n         }\n       });\n     },\n\n     // Excel导出功能\n     exportToExcel() {\n       this.exportLoading = true;\n\n       try {\n         // 准备导出数据\n         const exportData = this.pieName.map((name, index) => ({\n           '序号': index + 1,\n           '月份': name,\n           '预约数量': this.pieData[index] || 0\n         }));\n\n         // 创建工作簿\n         const ws = XLSX.utils.json_to_sheet(exportData);\n         const wb = XLSX.utils.book_new();\n         const sheetName = this.role === '医生' ? '我的月预约统计' : '月预约统计';\n         XLSX.utils.book_append_sheet(wb, ws, sheetName);\n\n         // 设置列宽\n         ws['!cols'] = [\n           { wch: 8 },  // 序号\n           { wch: 15 }, // 月份\n           { wch: 12 }  // 预约数量\n         ];\n\n         // 导出文件\n         const fileName = `${sheetName}_${new Date().toLocaleDateString().replace(/\\//g, '-')}.xlsx`;\n         XLSX.writeFile(wb, fileName);\n\n         this.$message.success('导出成功！');\n       } catch (error) {\n         console.error('导出失败:', error);\n         this.$message.error('导出失败，请重试');\n       } finally {\n         this.exportLoading = false;\n       }\n     }\n   }\n };\n</script>\n<style scoped>\n</style>\n \n\n"]}]}