{"remainingRequest": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue", "mtime": 1749226188799}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\babel.config.js", "mtime": 1749191414000}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1749193686703}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1749193685918}, {"path": "I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1749193686869}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCAkIGZyb20gJ2pxdWVyeSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTGVmdE1lbnUiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VyTG5hbWU6ICIiLAogICAgICByb2xlOiAiIiwKICAgICAgYWN0aXZlTWVudTogbnVsbCwKICAgICAgLy8g55So5LqO6Lef6Liq5b2T5YmN5r+A5rS755qE6I+c5Y2VCiAgICAgIHNob3dleGlzdDogZmFsc2UKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy51c2VyTG5hbWUgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCJ1c2VyTG5hbWUiKTsKICAgIHRoaXMucm9sZSA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInJvbGUiKTsKCiAgICAvLyDkv67mlLnoj5zljZXngrnlh7vkuovku7YKICAgICQoJy5oYXMtYXJyb3cnKS5jbGljayhmdW5jdGlvbiAoZSkgewogICAgICBlLnByZXZlbnREZWZhdWx0KCk7CgogICAgICAvLyDkuLrniLZsaeWFg+e0oOa3u+WKoG1tLWFjdGl2ZeexuwogICAgICAkKHRoaXMpLnBhcmVudCgnbGknKS5hZGRDbGFzcygnbW0tYWN0aXZlJyk7CiAgICAgICQodGhpcykubmV4dCgndWwnKS50b2dnbGVDbGFzcygnbW0tc2hvdycpOwoKICAgICAgLy8g5YWz6Zet5YW25LuW5omT5byA55qE6I+c5Y2VCiAgICAgICQoJy5oYXMtYXJyb3cnKS5ub3QodGhpcykucGFyZW50KCdsaScpLnJlbW92ZUNsYXNzKCdtbS1hY3RpdmUnKTsKICAgICAgJCgnLmhhcy1hcnJvdycpLm5vdCh0aGlzKS5uZXh0KCd1bCcpLnJlbW92ZUNsYXNzKCdtbS1zaG93Jyk7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHRvZ2dsZVNob3dFeGlzdCgpIHsKICAgICAgdGhpcy5zaG93ZXhpc3QgPSAhdGhpcy5zaG93ZXhpc3Q7CiAgICAgIGlmICh0aGlzLnNob3dleGlzdCkgewogICAgICAgICQoIi5kcm9wZG93bi1tZW51IikucmVtb3ZlQ2xhc3MoInNob3ciKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAkKCIuZHJvcGRvd24tbWVudSIpLmFkZENsYXNzKCJzaG93Iik7CiAgICAgIH0KICAgIH0sCiAgICBleGl0OiBmdW5jdGlvbiAoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuehruiupOmAgOWHuuWQlz8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgidXNlckxuYW1lIik7CiAgICAgICAgc2Vzc2lvblN0b3JhZ2UucmVtb3ZlSXRlbSgicm9sZSIpOwogICAgICAgIF90aGlzLiRyb3V0ZXIucHVzaCgiLyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["$", "name", "data", "userLname", "role", "activeMenu", "showexist", "mounted", "sessionStorage", "getItem", "click", "e", "preventDefault", "parent", "addClass", "next", "toggleClass", "not", "removeClass", "methods", "toggleShowExist", "exit", "_this", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "$router", "push", "catch"], "sources": ["I:\\product4\\00452HospitalRegistrationBooking\\OnlineMedicalAppointmentSystem-web\\src\\components\\LeftMenu.vue"], "sourcesContent": ["<template>\r\n\r\n  <div class=\"deznav\" style=\"background:#8BC34A;\">\r\n    <div class=\"deznav-scroll mm-active\">\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '管理员'\">\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">患者管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/usersManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理患者</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">科室管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/partsAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加科室</router-link></li>\r\n            <li><router-link to=\"/partsManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理科室</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">医生管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/doctorAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加医生</router-link></li>\r\n            <li><router-link to=\"/doctorManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理医生</router-link>\r\n            </li>\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/plansAdd\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>添加排班</router-link></li>\r\n            <li><router-link to=\"/plansManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理排班</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>管理预约挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/total1\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>科室预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total2\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>医生预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">系统管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/mailremindertemplateEdit\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 邮件模板设置\r\n              </router-link></li>\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n      <ul class=\"metismenu mm-show\" id=\"menu\" v-show=\"role === '医生'\">\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">排班管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n     \r\n            <li><router-link to=\"/plansManage2\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我的排班日历</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">聊天管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/chatinfoManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我收到的聊天</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">预约挂号管理</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n            <li><router-link to=\"/reserveManage\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>我收到的挂号</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n   \r\n\r\n                <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">统计报表</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n         \r\n              \r\n                  <li><router-link to=\"/total3\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>日预约统计</router-link>\r\n            </li>\r\n                  <li><router-link to=\"/total4\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>月预约统计</router-link>\r\n            </li>\r\n\r\n          </ul>\r\n        </li>\r\n\r\n\r\n        <li><a class=\"has-arrow \" href=\"javascript:void(0);\" aria-expanded=\"false\">\r\n            <div class=\"menu-icon\">\r\n              <svg width=\"22\" height=\"22\" viewBox=\"0 0 22 22\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 5.95833C2.75 3.55189 2.77577 2.75 5.95833 2.75C9.1409 2.75 9.16667 3.55189 9.16667 5.95833C9.16667 8.36478 9.17682 9.16667 5.95833 9.16667C2.73985 9.16667 2.75 8.36478 2.75 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 5.95833C12.8333 3.55189 12.8591 2.75 16.0416 2.75C19.2242 2.75 19.25 3.55189 19.25 5.95833C19.25 8.36478 19.2601 9.16667 16.0416 9.16667C12.8232 9.16667 12.8333 8.36478 12.8333 5.95833Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M2.75 16.0417C2.75 13.6352 2.77577 12.8333 5.95833 12.8333C9.1409 12.8333 9.16667 13.6352 9.16667 16.0417C9.16667 18.4481 9.17682 19.25 5.95833 19.25C2.73985 19.25 2.75 18.4481 2.75 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\"\r\n                  d=\"M12.8333 16.0417C12.8333 13.6352 12.8591 12.8333 16.0416 12.8333C19.2242 12.8333 19.25 13.6352 19.25 16.0417C19.25 18.4481 19.2601 19.25 16.0416 19.25C12.8232 19.25 12.8333 18.4481 12.8333 16.0417Z\"\r\n                  stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"></path>\r\n              </svg>\r\n            </div>\r\n            <span class=\"nav-text\">个人中心</span>\r\n          </a>\r\n          <ul aria-expanded=\"false\" class=\"mm-collapse left\" style=\"\">\r\n               <li><router-link to=\"/doctorInfo\" class=\"sub-menu-item\"><i class=\"el-icon-right\"></i>修改个人信息</router-link>\r\n            </li>\r\n            <li><router-link to=\"/password\" class=\"sub-menu-item\">\r\n                <i class=\"el-icon-right\"></i> 修改密码\r\n              </router-link></li>\r\n          </ul>\r\n        </li>\r\n\r\n\r\n\r\n      </ul>\r\n\r\n    </div>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport $ from 'jquery';\r\n\r\nexport default {\r\n  name: \"LeftMenu\",\r\n  data() {\r\n    return {\r\n      userLname: \"\",\r\n      role: \"\",\r\n      activeMenu: null, // 用于跟踪当前激活的菜单\r\n      showexist: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userLname = sessionStorage.getItem(\"userLname\");\r\n    this.role = sessionStorage.getItem(\"role\");\r\n\r\n    // 修改菜单点击事件\r\n    $('.has-arrow').click(function (e) {\r\n      e.preventDefault();\r\n\r\n      // 为父li元素添加mm-active类\r\n      $(this).parent('li').addClass('mm-active');\r\n\r\n      $(this).next('ul').toggleClass('mm-show');\r\n\r\n      // 关闭其他打开的菜单\r\n      $('.has-arrow').not(this).parent('li').removeClass('mm-active');\r\n      $('.has-arrow').not(this).next('ul').removeClass('mm-show');\r\n    });\r\n\r\n\r\n  },\r\n  methods: {\r\n\r\n    toggleShowExist() {\r\n      this.showexist = !this.showexist;\r\n\r\n      if (this.showexist) {\r\n        $(\".dropdown-menu\").removeClass(\"show\");\r\n      } else {\r\n        $(\".dropdown-menu\").addClass(\"show\");\r\n      }\r\n\r\n    },\r\n\r\n    exit: function () {\r\n      var _this = this;\r\n      this.$confirm(\"确认退出吗?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          sessionStorage.removeItem(\"userLname\");\r\n          sessionStorage.removeItem(\"role\");\r\n          _this.$router.push(\"/\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.sub-menu-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 15px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sub-menu-item:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.sub-menu-item i {\r\n  margin-right: 10px;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n"], "mappings": ";AAqWA,OAAOA,CAAA,MAAO,QAAQ;AAEtB,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,EAAE;MACbC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,IAAI;MAAE;MAClBC,SAAS,EAAE;IACb,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACJ,SAAQ,GAAIK,cAAc,CAACC,OAAO,CAAC,WAAW,CAAC;IACpD,IAAI,CAACL,IAAG,GAAII,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;;IAE1C;IACAT,CAAC,CAAC,YAAY,CAAC,CAACU,KAAK,CAAC,UAAUC,CAAC,EAAE;MACjCA,CAAC,CAACC,cAAc,CAAC,CAAC;;MAElB;MACAZ,CAAC,CAAC,IAAI,CAAC,CAACa,MAAM,CAAC,IAAI,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC;MAE1Cd,CAAC,CAAC,IAAI,CAAC,CAACe,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,CAAC,SAAS,CAAC;;MAEzC;MACAhB,CAAC,CAAC,YAAY,CAAC,CAACiB,GAAG,CAAC,IAAI,CAAC,CAACJ,MAAM,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC,WAAW,CAAC;MAC/DlB,CAAC,CAAC,YAAY,CAAC,CAACiB,GAAG,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC,CAACG,WAAW,CAAC,SAAS,CAAC;IAC7D,CAAC,CAAC;EAGJ,CAAC;EACDC,OAAO,EAAE;IAEPC,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACd,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;MAEhC,IAAI,IAAI,CAACA,SAAS,EAAE;QAClBN,CAAC,CAAC,gBAAgB,CAAC,CAACkB,WAAW,CAAC,MAAM,CAAC;MACzC,OAAO;QACLlB,CAAC,CAAC,gBAAgB,CAAC,CAACc,QAAQ,CAAC,MAAM,CAAC;MACtC;IAEF,CAAC;IAEDO,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,IAAIC,KAAI,GAAI,IAAI;MAChB,IAAI,CAACC,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE;QAC5BC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,EACEC,IAAI,CAAC,MAAM;QACVnB,cAAc,CAACoB,UAAU,CAAC,WAAW,CAAC;QACtCpB,cAAc,CAACoB,UAAU,CAAC,MAAM,CAAC;QACjCN,KAAK,CAACO,OAAO,CAACC,IAAI,CAAC,GAAG,CAAC;MACzB,CAAC,EACAC,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;IACrB;EACF;AACF,CAAC", "ignoreList": []}]}